from omnicat.ingestor import WeldersArc<PERSON>nges<PERSON>, OmnicatIngestor
from omnicat.ingestor.welders_arc.models import MessageHeader, Elset_Full, Elset_Sgp4Xp
from omnicat import OmniCat
from kafka.consumer.fetcher import TopicPartition
from kafka import KafkaConsumer
from kafka.consumer.fetcher import ConsumerRecord
from unittest.mock import MagicMock
from datetime import datetime, timezone, timedelta
import uuid
import json

# Import the mock UDL reader function from the shared test module
from tests.integration.test_aoi_query import create_mock_udl_reader_with_correlated_elsets


def test_welders_arc_ingestion_and_aoi_query(
    neo4j_db,
    postgis_db,
    aoi_query_time_window,
    tracks_in_aoi,
    tracks_outside_aoi,
    aoi
):
    """
    Test WeldersArcIngestor by mocking Kafka consumer to return predefined messages of both types:
    1. Elset_Sgp4Xp messages on the sgp4-xp topic
    2. Elset_UctCandidate messages on the uct-candidate topic
    After ingestion, query tracks within AOI and verify both types are correctly processed.
    """
    # Create a dictionary of TLE data from in_aoi tracks for later comparison
    in_aoi_tles = {(track.data.line1, track.data.line2)                   : True for track in tracks_in_aoi}

    # Create mock Kafka consumer
    mock_consumer = create_mock_kafka_consumer(
        tracks_in_aoi, tracks_outside_aoi)

    # Create mock UdlReader
    mock_udl_reader = create_mock_udl_reader_with_correlated_elsets(
        tracks_in_aoi, tracks_outside_aoi)

    # Create OmnicatIngestor with test databases
    omnicat_ingestor = OmnicatIngestor(neo4j_db, postgis_db)

    # Create WeldersArcIngestor with mock consumer and mock UdlReader
    welders_arc_ingestor = WeldersArcIngestor(
        consumer=mock_consumer,
        omnicat_ingestor=omnicat_ingestor,
        poll_timeout_ms=1000,
        batch_size=1000,
        udl_reader=mock_udl_reader
    )

    # Get time window from AOI for ingestion
    # Add buffer for ingestion window
    window_start = aoi.time_start - timedelta(days=1)
    window_end = aoi.time_end + timedelta(days=1)

    # Ingest tracks using WeldersArcIngestor
    welders_arc_ingestor.ingest(window_start, window_end)

    # Create OmniCat instance with test databases for querying
    omnicat = OmniCat(neo4j_db, postgis_db)

    # Query tracks within aoi
    result_ids, result_tracks = omnicat.query_tracks_within_aoi(aoi)

    # Verify results
    assert len(
        result_tracks) == 4, f"Expected 4 tracks in aoi, found {len(result_tracks)}"

    # Verify all tracks in result are from the "in_aoi" test tracks
    for track in result_tracks:
        # Use TLE data directly from the track object
        tle_key = (track.data.line1, track.data.line2)
        assert tle_key in in_aoi_tles, f"Found track with TLE not in original in_aoi tracks: {tle_key}"

        # Verify tracks have object correlations from the UDL integration
        assert track.object_correlations is not None, "Track should have object correlations"
        assert len(
            track.object_correlations) > 0, "Track should have at least one object correlation"

        # Verify each object correlation has the expected attributes
        for correlation in track.object_correlations:
            assert correlation.object_id is not None, "Object correlation missing object_id"
            assert correlation.validity_time is not None, "Object correlation missing validity_time"
            assert correlation.confidence == 1.0, "Object correlation confidence should be 1.0"

        # Verify message header is present in metadata
        assert "welders_arc_message_headers" in track.metadata, "Track missing welders_arc_message_headers in metadata"
        header = track.metadata["welders_arc_message_headers"]
        assert header is not None, "Message header should not be None"


def create_welders_arc_elset_from_track(track, idx) -> Elset_Sgp4Xp:
    """Convert a Track with TLE data to an Elset_Sgp4Xp object."""
    tle_data = track.data
    line1 = tle_data.line1
    line2 = tle_data.line2

    # Extract TLE fields
    # Line 1 fields
    catalog_number = line1[2:7].strip()
    # Classification (U=Unclassified, C=Classified, S=Secret)
    classification = line1[7:8]
    launch_year = line1[9:11]
    launch_number = line1[11:14]
    launch_piece = line1[14:17]
    epoch_year = int(line1[18:20])
    epoch_day = float(line1[20:32])
    mean_motion_dot = float(line1[33:43])
    mean_motion_ddot = float(f"{line1[44:45]}.{line1[45:50]}e{line1[50:52]}")
    bstar = float(f"{line1[53:54]}.{line1[54:59]}e{line1[59:61]}")
    ephemeris_type = line1[62:63]
    element_number = int(line1[64:68])
    checksum = int(line1[68:69])

    # Line 2 fields
    inclination = float(line2[8:16])
    raan = float(line2[17:25])
    eccentricity = float(f"0.{line2[26:33]}")
    arg_perigee = float(line2[34:42])
    mean_anomaly = float(line2[43:51])
    mean_motion = float(line2[52:63])
    rev_number = int(line2[63:68])

    # Convert two-digit year to four-digit year
    full_year = 2000 + epoch_year if epoch_year < 50 else 1900 + epoch_year

    # Calculate epoch date
    start_of_year = datetime(full_year, 1, 1, tzinfo=timezone.utc)
    epoch = start_of_year + timedelta(days=epoch_day - 1)

    # Create Elset_Sgp4Xp
    return Elset_Sgp4Xp(
        idElset=f"test_elset_{idx}",
        classificationMarking="UNCLASSIFIED",  # Default classification
        satNo=catalog_number,
        epoch=epoch.isoformat(),
        line1=line1,
        line2=line2,
        createdAt=datetime.now(timezone.utc).isoformat(),
        createdBy="test",
        origin="PROVIDER",
        source=track.source,
        ephemType=int(ephemeris_type) if ephemeris_type.isdigit() else 0,
        uct=False,
        origObjectId=f"test_object_{idx}",
        dataMode="REAL",  # Default data mode
        algorithm="SGP4XP",
        origNetwork="SLR"
    )


def create_welders_arc_uctcandidate_from_track(track, idx) -> Elset_Sgp4Xp:
    """Convert a Track with TLE data to an Elset_UctCandidate object."""
    tle_data = track.data
    line1 = tle_data.line1
    line2 = tle_data.line2

    # Extract TLE fields
    # Line 1 fields
    catalog_number = line1[2:7].strip()
    classification = line1[7:8]
    launch_year = line1[9:11]
    launch_number = line1[11:14]
    launch_piece = line1[14:17]
    epoch_year = int(line1[18:20])
    epoch_day = float(line1[20:32])
    mean_motion_dot = float(line1[33:43])
    mean_motion_ddot = float(f"{line1[44:45]}.{line1[45:50]}e{line1[50:52]}")
    bstar = float(f"{line1[53:54]}.{line1[54:59]}e{line1[59:61]}")
    ephemeris_type = line1[62:63]
    element_number = int(line1[64:68])

    # Line 2 fields
    inclination = float(line2[8:16])
    raan = float(line2[17:25])
    eccentricity = float(f"0.{line2[26:33]}")
    arg_perigee = float(line2[34:42])
    mean_anomaly = float(line2[43:51])
    mean_motion = float(line2[52:63])
    rev_number = int(line2[63:68])

    # Convert two-digit year to four-digit year
    full_year = 2000 + epoch_year if epoch_year < 50 else 1900 + epoch_year

    # Calculate epoch date
    start_of_year = datetime(full_year, 1, 1, tzinfo=timezone.utc)
    epoch = start_of_year + timedelta(days=epoch_day - 1)

    # Create Elset_UctCandidate
    from omnicat.ingestor.welders_arc.models import Elset_UctCandidate
    return Elset_UctCandidate(
        idElset=f"test_uct_elset_{idx}",
        classificationMarking="UNCLASSIFIED",
        satNo=int(catalog_number) if catalog_number.isdigit() else 0,
        epoch=epoch,
        meanMotion=mean_motion,
        idOnOrbit=f"test_object_{idx}",
        eccentricity=eccentricity,
        inclination=inclination,
        raan=raan,
        argOfPerigee=arg_perigee,
        meanAnomaly=mean_anomaly,
        revNo=rev_number,
        bStar=bstar,
        meanMotionDot=mean_motion_dot,
        meanMotionDDot=mean_motion_ddot,
        semiMajorAxis=0.0,  # Not in TLE, would need to calculate
        period=0.0,  # Not in TLE, would need to calculate
        apogee=0.0,  # Not in TLE, would need to calculate
        perigee=0.0,  # Not in TLE, would need to calculate
        line1=line1,
        line2=line2,
        sourcedData=["ELSET"],
        sourcedDataTypes=["ELSET"],  # Using string value instead of enum
        createdBy="test",
        source=track.source,
        dataMode="REAL",  # Using string value instead of enum
        algorithm="SGP4",  # Using string value instead of enum
        origNetwork="SLR"  # Using string value instead of enum
    )


def create_mock_kafka_consumer(tracks_in_aoi, tracks_outside_aoi):
    """Create a mock Kafka consumer that returns predefined ConsumerRecord objects."""
    mock_consumer = MagicMock(spec=KafkaConsumer)

    # Split tracks for different message types
    tracks_in_aoi_sgp4 = tracks_in_aoi[:len(tracks_in_aoi)//2]
    tracks_in_aoi_uct = tracks_in_aoi[len(tracks_in_aoi)//2:]

    tracks_outside_aoi_sgp4 = tracks_outside_aoi[:len(tracks_outside_aoi)//2]
    tracks_outside_aoi_uct = tracks_outside_aoi[len(tracks_outside_aoi)//2:]

    # Convert Track objects to Elset_Sgp4Xp objects
    elsets_in_aoi_sgp4 = [
        create_welders_arc_elset_from_track(track, idx)
        for idx, track in enumerate(tracks_in_aoi_sgp4)
    ]

    # Convert Track objects to Elset_UctCandidate objects
    elsets_in_aoi_uct = [
        create_welders_arc_uctcandidate_from_track(
            track, idx + len(tracks_in_aoi_sgp4))
        for idx, track in enumerate(tracks_in_aoi_uct)
    ]

    print(
        f"Created {len(elsets_in_aoi_sgp4)} SGP4 elset objects for tracks in AOI")
    print(
        f"Created {len(elsets_in_aoi_uct)} UCT elset objects for tracks in AOI")

    # Same for outside AOI tracks
    elsets_outside_aoi_sgp4 = [
        create_welders_arc_elset_from_track(track, idx + len(tracks_in_aoi))
        for idx, track in enumerate(tracks_outside_aoi_sgp4)
    ]

    elsets_outside_aoi_uct = [
        create_welders_arc_uctcandidate_from_track(
            track, idx + len(tracks_in_aoi) + len(tracks_outside_aoi_sgp4))
        for idx, track in enumerate(tracks_outside_aoi_uct)
    ]

    print(
        f"Created {len(elsets_outside_aoi_sgp4)} SGP4 elset objects for tracks outside AOI")
    print(
        f"Created {len(elsets_outside_aoi_uct)} UCT elset objects for tracks outside AOI")

    # Prepare messages for both topics
    sgp4_messages = create_messages_for_topic(
        elsets_in_aoi_sgp4 + elsets_outside_aoi_sgp4,
        "ss2.data.elset.sgp4-xp"
    )

    uct_messages = create_messages_for_topic(
        elsets_in_aoi_uct + elsets_outside_aoi_uct,
        "ss2.data.elset.uct-candidate"
    )

    print(f"Created {len(sgp4_messages)} SGP4 Kafka messages")
    print(f"Created {len(uct_messages)} UCT Kafka messages")

    # Configure mock consumer to return messages from both topics
    mock_consumer.poll.return_value = {
        TopicPartition("ss2.data.elset.sgp4-xp", 0): sgp4_messages,
        TopicPartition("ss2.data.elset.uct-candidate", 0): uct_messages
    }

    return mock_consumer


def create_messages_for_topic(elsets, topic_name):
    """Helper function to create Kafka messages for a specific topic."""
    messages = []
    for idx, elset in enumerate(elsets):
        message_id = str(uuid.uuid4())
        message_time = datetime.now(timezone.utc)

        # Set data type based on topic
        if topic_name == "ss2.data.elset.sgp4-xp":
            data_type = "ss2.data.elset.sgp4-xp"
        else:
            data_type = "ss2.data.elset.uct-candidate"

        # Create headers
        headers = [
            ('messageTime', message_time.isoformat().encode('utf-8')),
            ('subsystem', b'"ss2"'),
            ('messageVersion', b'"0.1.0"'),
            ('dataVersion', b'"0.1.0"'),
            ('dataType', f'"{data_type}"'.encode('utf-8')),
            ('messageId', f'"{message_id}"'.encode('utf-8')),
            ('traceability', json.dumps({
                "external": [{
                    "resourceLink": f"https://unifieddatalibrary.com/udl/elset/{elset.idElset}"
                }]
            }).encode('utf-8')),
            ('dataProvider', b'"Lynx"')
        ]

        # Convert to JSON to simulate the Kafka message format
        elset_json = elset.model_dump(mode="json")

        # Create a ConsumerRecord object
        mock_message = ConsumerRecord(
            topic=topic_name,
            partition=0,
            offset=idx,
            timestamp=message_time.timestamp() * 1000,
            timestamp_type=0,  # CREATE_TIME
            key=None,
            value=elset_json,
            headers=headers,
            checksum=None,
            serialized_key_size=-1,
            serialized_value_size=len(json.dumps(elset_json)),
            serialized_header_size=sum(len(k) + len(v) for k, v in headers),
            leader_epoch=0
        )

        # Ensure the message value is a dict
        assert isinstance(
            mock_message.value, dict), f"Message {idx} value is not a dict: {type(mock_message.value)}"
        # Ensure TLE fields are present
        assert "line1" in mock_message.value, f"Message {idx} missing line1"
        assert "line2" in mock_message.value, f"Message {idx} missing line2"

        messages.append(mock_message)

    return messages
